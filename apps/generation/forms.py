from django import forms
from .models import Lesson, Language


class LessonCreationForm(forms.ModelForm):
    """Form for creating a new lesson with topic and module type selection"""
    
    MODULE_TYPE_CHOICES = [
        ('discussion', 'Discussion'),
        ('reading', 'Reading'),
        ('vocabulary', 'Vocabulary'),
    ]
    
    topic = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'input input-bordered w-full',
            'placeholder': 'Enter lesson topic (e.g., "Travel and Tourism")',
        }),
        help_text="What topic should this lesson cover?"
    )
    
    keywords = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'textarea textarea-bordered w-full',
            'rows': 3,
            'placeholder': 'Enter keywords separated by commas (e.g., "airport, hotel, vacation")',
        }),
        help_text="Keywords to include in the lesson content"
    )
    
    module_type = forms.ChoiceField(
        choices=MODULE_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'radio radio-primary',
        }),
        help_text="What type of module would you like to create?"
    )
    
    language = forms.ModelChoiceField(
        queryset=Language.objects.all(),
        widget=forms.Select(attrs={
            'class': 'select select-bordered w-full',
        }),
        help_text="What language should this lesson be in?",
        empty_label="Select a language"
    )
    
    class Meta:
        model = Lesson
        fields = ['topic', 'keywords', 'language', 'level']
        
    def __init__(self, *args, **kwargs):
        level = kwargs.pop('level', None)
        super().__init__(*args, **kwargs)
        
        if level:
            self.fields['level'].initial = level
            self.fields['level'].widget = forms.HiddenInput()
        else:
            self.fields['level'].widget = forms.Select(attrs={
                'class': 'select select-bordered w-full',
            })
