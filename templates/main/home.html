{% extends 'base.html' %}
{% load module_tags %}

{% block content %}
<section class="flex flex-col items-center justify-center pt-3">
        {% typewriter text_list='["Imagine any lesson", "Immediately Made", "Making teaching easier!"]' class_name="text-4xl font-bold" %}
</section>

<!-- Lesson Creation Section -->
<section class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Create Your Lesson</h2>
            <p class="text-lg text-gray-600">Choose your difficulty level to get started</p>
        </div>

        <!-- Lesson Creation Container -->
        <div id="lesson-creation-container"
             x-data="lessonCreation()"
             class="bg-white rounded-lg shadow-lg p-8">

            <!-- Step 1: Difficulty Level Selection -->
            <div id="step-one"
                 x-show="currentStep === 1"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95">

                <h3 class="text-xl font-semibold text-center mb-6">Select Difficulty Level</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <button @click="selectLevel('Beginner')"
                            class="level-btn bg-green-100 hover:bg-green-200 text-green-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">🌱</div>
                        <div>Beginner</div>
                    </button>
                    <button @click="selectLevel('Elementary')"
                            class="level-btn bg-blue-100 hover:bg-blue-200 text-blue-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">📚</div>
                        <div>Elementary</div>
                    </button>
                    <button @click="selectLevel('Intermediate')"
                            class="level-btn bg-yellow-100 hover:bg-yellow-200 text-yellow-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">⚡</div>
                        <div>Intermediate</div>
                    </button>
                    <button @click="selectLevel('Upper Intermediate')"
                            class="level-btn bg-orange-100 hover:bg-orange-200 text-orange-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">🚀</div>
                        <div>Upper Intermediate</div>
                    </button>
                    <button @click="selectLevel('Advanced')"
                            class="level-btn bg-red-100 hover:bg-red-200 text-red-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">🎯</div>
                        <div>Advanced</div>
                    </button>
                    <button @click="selectLevel('Proficient')"
                            class="level-btn bg-purple-100 hover:bg-purple-200 text-purple-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
                        <div class="text-lg">👑</div>
                        <div>Proficient</div>
                    </button>
                </div>
            </div>

            <!-- Step 2: Topic and Module Selection -->
            <div id="step-two"
                 x-show="currentStep === 2"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform translate-x-full"
                 x-transition:enter-end="opacity-100 transform translate-x-0"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform translate-x-0"
                 x-transition:leave-end="opacity-0 transform -translate-x-full">

                <div class="text-center mb-6">
                    <h3 class="text-xl font-semibold">Create Your <span x-text="selectedLevel"></span> Lesson</h3>
                    <p class="text-gray-600 mt-2">Tell us about your lesson</p>
                </div>

                <div id="lesson-form-container">
                    <!-- Form will be loaded here via HTMX -->
                </div>
            </div>

            <!-- Loading State -->
            <div x-show="isLoading" class="text-center py-8">
                <div class="loading loading-spinner loading-lg"></div>
                <p class="mt-4 text-gray-600">Creating your lesson...</p>
            </div>
        </div>
    </div>
</section>

{% endblock %}

{% block javascript %}
<script>
function lessonCreation() {
    return {
        currentStep: 1,
        selectedLevel: '',
        isLoading: false,

        selectLevel(level) {
            this.selectedLevel = level;
            this.currentStep = 2;

            // Load the form via HTMX
            htmx.ajax('GET', `/generation/create-lesson/step-two/?level=${encodeURIComponent(level)}`, {
                target: '#lesson-form-container'
            });
        },

        submitForm() {
            this.isLoading = true;
        },

        resetForm() {
            this.currentStep = 1;
            this.selectedLevel = '';
            this.isLoading = false;
        }
    }
}
</script>
{% endblock %}