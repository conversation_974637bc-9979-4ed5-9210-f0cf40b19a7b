# Generated manually to add default languages

from django.db import migrations


def add_default_languages(apps, schema_editor):
    """Add some default languages to the database"""
    Language = apps.get_model('generation', 'Language')
    
    default_languages = [
        {'name': 'English', 'code': 'en'},
        {'name': 'Spanish', 'code': 'es'},
        {'name': 'French', 'code': 'fr'},
        {'name': 'German', 'code': 'de'},
        {'name': 'Italian', 'code': 'it'},
        {'name': 'Portuguese', 'code': 'pt'},
        {'name': 'Chinese', 'code': 'zh'},
        {'name': 'Japanese', 'code': 'ja'},
        {'name': 'Korean', 'code': 'ko'},
        {'name': 'Russian', 'code': 'ru'},
    ]
    
    for lang_data in default_languages:
        Language.objects.get_or_create(
            code=lang_data['code'],
            defaults={'name': lang_data['name']}
        )


def remove_default_languages(apps, schema_editor):
    """Remove the default languages (reverse operation)"""
    Language = apps.get_model('generation', 'Language')
    
    default_codes = ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko', 'ru']
    Language.objects.filter(code__in=default_codes).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('generation', '0003_remove_lesson_style_alter_discussion_lesson_and_more'),
    ]

    operations = [
        migrations.RunPython(add_default_languages, remove_default_languages),
    ]
