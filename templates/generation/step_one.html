<!-- This template is not used directly in the home page implementation -->
<!-- The level selection is built directly into the home.html template -->
<!-- This file is kept for potential future use or alternative implementations -->

<div class="text-center">
    <h3 class="text-xl font-semibold mb-6">Select Difficulty Level</h3>
    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        {% for value, label in levels %}
        <button onclick="selectLevel('{{ value }}')" 
                class="level-btn bg-blue-100 hover:bg-blue-200 text-blue-800 font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md">
            {{ label }}
        </button>
        {% endfor %}
    </div>
</div>
