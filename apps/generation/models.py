import os
import json

import auto_prefetch
from django.db import models
from google import genai
from google.genai import types

from model_utils.models import TimeStampedModel


class Language(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10)

    def __str__(self):
        return self.name


class Lesson(auto_prefetch.Model, TimeStampedModel):
    """The core part of a generation for our application"""

    class LevelChoices(models.TextChoices):
        BEGINNER = "Beginner", "Beginner"
        ELEMENTARY = "Elementary", "Elementary"
        INTERMEDIATE = "Intermediate", "Intermediate"
        UPPER_INTERMEDIATE = "Upper Intermediate", "Upper Intermediate"
        ADVANCED = "Advanced", "Advanced"
        PROFICIENT = "Proficient", "Proficient"

    level = models.CharField(choices=LevelChoices.choices, max_length=20)

    language = models.ForeignKey(Language, on_delete=models.CASCADE)

    topic = models.Char<PERSON>ield(max_length=100)

    keywords = models.TextField()

    selected_title = models.CharField(max_length=100, null=True, blank=False)

    # models.py (add to Lesson class)
    @property
    def modules(self):
        """Get all lesson modules sorted by creation time"""
        modules = []
        modules.extend(self.discussions.all())
        modules.extend(self.readings.all())
        modules.extend(self.vocabularies.all())
        modules.sort(key=lambda x: x.created)
        return modules

    def generate_titles(self) -> list[str]:
        """Hit the google api and generate titles for the lesson"""

        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.5-flash-preview-05-20"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Generate titles that for the topic of {self.topic}. Make sure the titles are in {self.language.name}"""
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "titles": genai.types.Schema(
                        type=genai.types.Type.ARRAY,
                        items=genai.types.Schema(
                            type=genai.types.Type.STRING,
                        ),
                    ),
                },
            ),
        )

        full_json_string = ""

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Append the chunk text to the temporary string
            if chunk.text:  # Ensure chunk.text is not None or empty
                full_json_string += chunk.text

        # After the loop, parse the accumulated string into a Python object
        if full_json_string:  # Check if any text was received
            return json.loads(full_json_string)
        else:
            # Handle case where no response text was received
            return {"error": "No content received from AI"}

    def generate_discussion(self):
        """Generate the discussion for the lesson"""
        discussion = Discussion.objects.create(lesson=self)
        discussion.generate_discussion_questions()

    def generate_vocabulary(self):
        """Generate the vocabulary for the lesson"""
        vocabulary = Vocabulary.objects.create(lesson=self)
        vocabulary.generate_words_to_learn()
        vocabulary.generate_sentences()

    def generate_reading(self):
        """Generate the reading for the lesson"""
        reading = Reading.objects.create(lesson=self)
        reading.generate_reading_text()


class BaseLessonModule(auto_prefetch.Model, TimeStampedModel):
    """Base class for all lesson modules"""

    lesson = models.ForeignKey(
        Lesson, on_delete=models.CASCADE, related_name="%(class)ss"
    )

    template = None

    class Meta(auto_prefetch.Model.Meta):
        abstract = True

    def get_template(self):
        try:
            assert self.template is not None
        except AssertionError:
            raise NotImplementedError("Subclasses must define a template")
        return self.template


class Discussion(BaseLessonModule):
    """
    Represents a discussion that is generated by the AI
    Discussions are part of every lesson.
    """

    questions = models.JSONField(help_text="6 questions to ask user", null=True)

    template = "pdf/discussion_questions.html"

    def generate_discussion_questions(self):
        """Hit the google api and generate questions for the discussion"""

        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.5-flash-preview-05-20"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Given the theme of {self.lesson.selected_title} generate a list of discussion questions for an {self.lesson.level} {self.lesson.language.name} speaker to practice their language skills. 
                        Make sure it contains the keywords {self.lesson.keywords}."""
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "questions": genai.types.Schema(
                        type=genai.types.Type.ARRAY,
                        items=genai.types.Schema(
                            type=genai.types.Type.STRING,
                        ),
                    ),
                },
            ),
        )

        full_json_string = ""

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Append the chunk text to the temporary string
            if chunk.text:  # Ensure chunk.text is not None or empty
                full_json_string += chunk.text

        # After the loop, parse the accumulated string into a Python object
        if full_json_string:  # Check if any text was received
            self.questions = json.loads(full_json_string)
        else:
            # Handle case where no response text was received
            self.questions = {"error": "No content received from AI"}

        self.save()


class Reading(BaseLessonModule):
    """
    Represents a reading that is generated by the AI
    """

    text = models.TextField()

    template = "pdf/reading.html"

    def generate_reading_text(self):
        """Hit the google api and generate reading text"""

        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.5-flash-preview-05-20"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Given the theme of {self.lesson.selected_title} generate a reading passage for an {self.lesson.level} {self.lesson.language.name} speaker to practice their language skills.
                        Make sure it contains the keywords {self.lesson.keywords}. The passage should be appropriate for the level and engaging."""
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "text": genai.types.Schema(
                        type=genai.types.Type.STRING,
                    ),
                },
            ),
        )

        full_json_string = ""

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Append the chunk text to the temporary string
            if chunk.text:  # Ensure chunk.text is not None or empty
                full_json_string += chunk.text

        # After the loop, parse the accumulated string into a Python object
        if full_json_string:  # Check if any text was received
            response_data = json.loads(full_json_string)
            self.text = response_data.get("text", "")
        else:
            # Handle case where no response text was received
            self.text = "Error: No content received from AI"

        self.save()


class Vocabulary(BaseLessonModule):
    """
    Represents a vocabulary that is generated by the AI

    """

    words = models.JSONField(help_text="10 words to learn")
    sentences = models.JSONField(
        help_text="10 sentences that are missing the words that the student will need to fill in the blank"
    )

    template = "pdf/vocabulary.html"

    def generate_words_to_learn(self):
        """Hit the google api and generate words to learn for the vocabulary"""

        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.5-flash-preview-05-20"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Given the theme of {self.lesson.selected_title} generate a list of 10 words that an {self.lesson.level} {self.lesson.language.name} speaker should learn."""
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "words": genai.types.Schema(
                        type=genai.types.Type.ARRAY,
                        items=genai.types.Schema(
                            type=genai.types.Type.STRING,
                        ),
                    ),
                },
            ),
        )

        full_json_string = ""

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Append the chunk text to the temporary string
            if chunk.text:  # Ensure chunk.text is not None or empty
                full_json_string += chunk.text

        # After the loop, parse the accumulated string into a Python object
        if full_json_string:  # Check if any text was received
            self.words = json.loads(full_json_string)
        else:
            # Handle case where no response text was received
            self.words = {"error": "No content received from AI"}

        self.save()

    def generate_sentences(self):
        """Hit the google api and generate sentences for the vocabulary"""

        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )

        model = "gemini-2.5-flash-preview-05-20"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Given these words {self.words} generate a list of 10 sentences that are missing the words that the student will need to fill in the blank."""
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "sentences": genai.types.Schema(
                        type=genai.types.Type.ARRAY,
                        items=genai.types.Schema(
                            type=genai.types.Type.STRING,
                        ),
                    ),
                },
            ),
        )

        full_json_string = ""

        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            # Append the chunk text to the temporary string
            if chunk.text:  # Ensure chunk.text is not None or empty
                full_json_string += chunk.text

        # After the loop, parse the accumulated string into a Python object
        if full_json_string:  # Check if any text was received
            self.sentences = json.loads(full_json_string)
        else:
            # Handle case where no response text was received
            self.sentences = {"error": "No content received from AI"}

        self.save()
