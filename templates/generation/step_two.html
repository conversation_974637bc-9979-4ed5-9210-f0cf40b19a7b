<!-- Step 2: Topic and Module Selection Form -->
<form hx-post="{% url 'generation:create_lesson_submit' %}" 
      hx-trigger="submit"
      hx-indicator="#loading-indicator"
      @submit="submitForm()"
      class="space-y-6">
    
    <!-- Hidden level field -->
    <input type="hidden" name="level" value="{{ level }}">
    
    <!-- Topic Input -->
    <div class="form-control">
        <label class="label">
            <span class="label-text font-semibold">Lesson Topic</span>
        </label>
        {{ form.topic }}
        <label class="label">
            <span class="label-text-alt text-gray-500">{{ form.topic.help_text }}</span>
        </label>
    </div>
    
    <!-- Language Selection -->
    <div class="form-control">
        <label class="label">
            <span class="label-text font-semibold">Language</span>
        </label>
        {{ form.language }}
        <label class="label">
            <span class="label-text-alt text-gray-500">{{ form.language.help_text }}</span>
        </label>
    </div>
    
    <!-- Keywords Input -->
    <div class="form-control">
        <label class="label">
            <span class="label-text font-semibold">Keywords</span>
        </label>
        {{ form.keywords }}
        <label class="label">
            <span class="label-text-alt text-gray-500">{{ form.keywords.help_text }}</span>
        </label>
    </div>
    
    <!-- Module Type Selection -->
    <div class="form-control">
        <label class="label">
            <span class="label-text font-semibold">Module Type</span>
        </label>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
            <label class="cursor-pointer">
                <input type="radio" name="module_type" value="discussion" class="radio radio-primary" checked>
                <div class="card bg-base-100 border-2 border-gray-200 hover:border-primary transition-colors p-4 ml-2">
                    <div class="text-center">
                        <div class="text-2xl mb-2">💬</div>
                        <div class="font-semibold">Discussion</div>
                        <div class="text-sm text-gray-500 mt-1">Interactive questions for conversation practice</div>
                    </div>
                </div>
            </label>
            
            <label class="cursor-pointer">
                <input type="radio" name="module_type" value="reading" class="radio radio-primary">
                <div class="card bg-base-100 border-2 border-gray-200 hover:border-primary transition-colors p-4 ml-2">
                    <div class="text-center">
                        <div class="text-2xl mb-2">📖</div>
                        <div class="font-semibold">Reading</div>
                        <div class="text-sm text-gray-500 mt-1">Engaging text passages for comprehension</div>
                    </div>
                </div>
            </label>
            
            <label class="cursor-pointer">
                <input type="radio" name="module_type" value="vocabulary" class="radio radio-primary">
                <div class="card bg-base-100 border-2 border-gray-200 hover:border-primary transition-colors p-4 ml-2">
                    <div class="text-center">
                        <div class="text-2xl mb-2">📝</div>
                        <div class="font-semibold">Vocabulary</div>
                        <div class="text-sm text-gray-500 mt-1">Word learning with fill-in-the-blank exercises</div>
                    </div>
                </div>
            </label>
        </div>
        <label class="label">
            <span class="label-text-alt text-gray-500">{{ form.module_type.help_text }}</span>
        </label>
    </div>
    
    <!-- Submit Button -->
    <div class="form-control mt-8">
        <button type="submit" class="btn btn-primary btn-lg">
            <span class="loading loading-spinner loading-sm" id="loading-indicator" style="display: none;"></span>
            Create Lesson
        </button>
    </div>
    
    <!-- Back Button -->
    <div class="form-control">
        <button type="button" @click="resetForm()" class="btn btn-ghost">
            ← Back to Level Selection
        </button>
    </div>
</form>

<script>
// Handle form submission response
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.status === 200) {
        try {
            const response = JSON.parse(event.detail.xhr.responseText);
            if (response.success) {
                // Show success message
                alert(response.message);
                // Reset the form
                if (typeof resetForm === 'function') {
                    resetForm();
                }
            } else {
                // Show error message
                alert('Error: ' + (response.message || 'Something went wrong'));
            }
        } catch (e) {
            console.error('Error parsing response:', e);
        }
    }
});

// Handle radio button selection styling
document.addEventListener('DOMContentLoaded', function() {
    const radioButtons = document.querySelectorAll('input[name="module_type"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove selected styling from all cards
            document.querySelectorAll('.card').forEach(card => {
                card.classList.remove('border-primary', 'bg-primary/10');
                card.classList.add('border-gray-200');
            });
            
            // Add selected styling to the selected card
            if (this.checked) {
                const card = this.nextElementSibling;
                card.classList.remove('border-gray-200');
                card.classList.add('border-primary', 'bg-primary/10');
            }
        });
    });
    
    // Set initial selection
    const checkedRadio = document.querySelector('input[name="module_type"]:checked');
    if (checkedRadio) {
        checkedRadio.dispatchEvent(new Event('change'));
    }
});
</script>
