from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.contrib import messages
from apps.generation.models import Lesson, Language
from .forms import LessonCreationForm


def create_lesson_step_one(request):
    """Display difficulty level selection"""
    levels = Lesson.LevelChoices.choices
    context = {
        'levels': levels,
    }
    return render(request, 'generation/step_one.html', context)


def create_lesson_step_two(request):
    """Display topic and module type selection form"""
    level = request.GET.get('level')
    if not level:
        return redirect('generation:create_lesson_step_one')

    form = LessonCreationForm(level=level)
    context = {
        'form': form,
        'level': level,
    }
    return render(request, 'generation/step_two.html', context)


def create_lesson_submit(request):
    """Handle lesson creation and module generation"""
    if request.method == 'POST':
        form = LessonCreationForm(request.POST)
        level = request.POST.get('level')
        module_type = request.POST.get('module_type')

        if form.is_valid():
            # Create the lesson
            lesson = form.save(commit=False)
            lesson.level = level
            lesson.save()

            # Generate a title for the lesson
            try:
                titles_response = lesson.generate_titles()
                if titles_response and 'titles' in titles_response:
                    lesson.selected_title = titles_response['titles'][0]
                    lesson.save()
            except Exception as e:
                lesson.selected_title = f"Lesson on {lesson.topic}"
                lesson.save()

            # Generate the selected module type
            try:
                if module_type == 'discussion':
                    lesson.generate_discussion()
                elif module_type == 'reading':
                    lesson.generate_reading()
                elif module_type == 'vocabulary':
                    lesson.generate_vocabulary()

                messages.success(request, f'Lesson "{lesson.selected_title}" created successfully with {module_type} module!')
                return JsonResponse({
                    'success': True,
                    'message': f'Lesson created successfully!',
                    'lesson_id': lesson.id
                })
            except Exception as e:
                messages.error(request, f'Lesson created but there was an error generating the {module_type} module.')
                return JsonResponse({
                    'success': False,
                    'message': f'Error generating module: {str(e)}'
                })
        else:
            return JsonResponse({
                'success': False,
                'errors': form.errors
            })

    return redirect('generation:create_lesson_step_one')


def generate_lesson_titles(request, lesson_id):
    """htmx view to return a list of titles made by the AI"""
    lesson = get_object_or_404(Lesson, pk=lesson_id)
    titles = lesson.generate_titles()

    context = {
        "titles": titles,
    }

    return render(request, "partials/generate_lesson_titles.html", context)
